import { and, eq, inArray } from "drizzle-orm";

import { db, Transaction } from "../../../../database/db";
import { conversationCallToActionCopyTable } from "../../../../database/schemas/advertising/conversationCallToActionCopy.table";
import { conversationMessageCopyTable } from "../../../../database/schemas/advertising/conversationMessageCopy.table";
import { conversationSubjectCopyTable } from "../../../../database/schemas/advertising/conversationSubjectCopy.table";
import { ITransaction } from "../../../core/application/interfaces/infrastructure/services/transactionManager.service.interface";
import { IConversationSubjectCopyRepository } from "../../application/interfaces/infrastructure/repositories/conversationSubjectCopy.repository.interface";
import { ConversationSubjectCopy } from "../../domain/entites/conversationSubjectCopy";

export class ConversationSubjectCopyRepository
  implements IConversationSubjectCopyRepository
{
  async deleteManyForAdSegmentByType(
    input: {
      adSegmentValuePropIds: string[];
      conversationCopyTypes: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (input.conversationCopyTypes.length === 0) {
      return;
    }
    if (input.adSegmentValuePropIds.length === 0) {
      return;
    }

    const onesToDelete = await invoker
      .select()
      .from(conversationSubjectCopyTable)
      .where(
        and(
          inArray(
            conversationSubjectCopyTable.adSegmentValuePropId,
            input.adSegmentValuePropIds,
          ),
          inArray(
            conversationSubjectCopyTable.type,
            input.conversationCopyTypes,
          ),
          eq(conversationSubjectCopyTable.status, input.status),
        ),
      );

    const subjectIdsToDelete = onesToDelete.map((each) => each.id);

    const messageCopyiesToDelete = await invoker
      .select()
      .from(conversationSubjectCopyTable)
      .where(
        and(
          inArray(
            conversationSubjectCopyTable.adSegmentValuePropId,
            input.adSegmentValuePropIds,
          ),
          inArray(
            conversationSubjectCopyTable.type,
            input.conversationCopyTypes,
          ),
          eq(conversationSubjectCopyTable.status, input.status),
        ),
      )
      .innerJoin(
        conversationMessageCopyTable,
        eq(
          conversationSubjectCopyTable.id,
          conversationMessageCopyTable.conversationSubjectCopyId,
        ),
      );

    const messageCopyIdsToDelete = messageCopyiesToDelete.map(
      (each) => each.conversation_message_copy.id,
    );

    const callToActionCopyiesToDelete = await invoker
      .select()
      .from(conversationSubjectCopyTable)
      .where(
        and(
          inArray(
            conversationSubjectCopyTable.adSegmentValuePropId,
            input.adSegmentValuePropIds,
          ),
          inArray(
            conversationSubjectCopyTable.type,
            input.conversationCopyTypes,
          ),
          eq(conversationSubjectCopyTable.status, input.status),
        ),
      )
      .innerJoin(
        conversationMessageCopyTable,
        eq(
          conversationSubjectCopyTable.id,
          conversationMessageCopyTable.conversationSubjectCopyId,
        ),
      )
      .innerJoin(
        conversationCallToActionCopyTable,
        eq(
          conversationMessageCopyTable.id,
          conversationCallToActionCopyTable.conversationMessageCopyId,
        ),
      );

    const callToActionCopyIdsToDelete = callToActionCopyiesToDelete.map(
      (each) => each.conversation_call_to_action_copy.id,
    );

    await invoker
      .delete(conversationCallToActionCopyTable)
      .where(
        and(
          inArray(
            conversationCallToActionCopyTable.id,
            callToActionCopyIdsToDelete,
          ),
          eq(conversationCallToActionCopyTable.status, input.status),
        ),
      );

    await invoker
      .delete(conversationMessageCopyTable)
      .where(
        and(
          inArray(conversationMessageCopyTable.id, messageCopyIdsToDelete),
          eq(conversationMessageCopyTable.status, input.status),
        ),
      );

    await invoker
      .delete(conversationSubjectCopyTable)
      .where(
        and(
          inArray(conversationSubjectCopyTable.id, subjectIdsToDelete),
          eq(conversationSubjectCopyTable.status, input.status),
        ),
      );
  }
  async getAllForValueProp(
    valuePropId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<ConversationSubjectCopy[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(conversationSubjectCopyTable)
      .where(
        and(
          eq(conversationSubjectCopyTable.adSegmentValuePropId, valuePropId),
          eq(conversationSubjectCopyTable.status, status),
        ),
      );
    return res.map((each) =>
      ConversationSubjectCopy({
        id: each.id,
        content: each.content,
        type: each.type,
        valuePropId: each.adSegmentValuePropId,
        leadGenForm: each.leadGenFormUrn,
        status: each.status,
      }),
    );
  }
  async createOneOrUpdateOneIfExists(
    input: ConversationSubjectCopy,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;

    console.log("REPOINPUT", input);
    await invoker
      .insert(conversationSubjectCopyTable)
      .values({
        id: input.id,
        content: input.content,
        type: input.type,
        adSegmentValuePropId: input.valuePropId,
        leadGenFormUrn: input.leadGenForm,
        destinationUrl: input.destinationUrl,
      })
      .onConflictDoUpdate({
        target: [
          conversationSubjectCopyTable.adSegmentValuePropId,
          conversationSubjectCopyTable.type,
        ],
        set: {
          content: input.content,
          leadGenFormUrn: input.leadGenForm,
          destinationUrl: input.destinationUrl,
        },
      });
  }
  async getOne(
    input: {
      valuePropId: string;
      conversationCopyType: ConversationSubjectCopy["type"];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: Transaction,
  ): Promise<ConversationSubjectCopy | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(conversationSubjectCopyTable)
      .where(
        and(
          eq(
            conversationSubjectCopyTable.adSegmentValuePropId,
            input.valuePropId,
          ),
          eq(conversationSubjectCopyTable.type, input.conversationCopyType),
          eq(conversationSubjectCopyTable.status, input.status),
        ),
      );
    if (!result[0]) {
      return null;
    }
    return ConversationSubjectCopy({
      id: result[0].id,
      content: result[0].content,
      type: result[0].type,
      valuePropId: result[0].adSegmentValuePropId,
      leadGenForm: result[0].leadGenFormUrn,
      destinationUrl: result[0].destinationUrl,
      status: result[0].status,
    });
  }
  async getId(
    input: {
      valuePropId: string;
      conversationCopyType: ConversationSubjectCopy["type"];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: Transaction,
  ): Promise<string | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select({
        id: conversationSubjectCopyTable.id,
      })
      .from(conversationSubjectCopyTable)
      .where(
        and(
          eq(
            conversationSubjectCopyTable.adSegmentValuePropId,
            input.valuePropId,
          ),
          eq(conversationSubjectCopyTable.type, input.conversationCopyType),
          eq(conversationSubjectCopyTable.status, input.status),
        ),
      );
    if (!result[0]) {
      return null;
    }
    return result[0].id;
  }
  async deleteManyForLinkedInAdSegmentValueProps(
    input: {
      linkedInAdSegmentValuePropIds: string[];
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (input.linkedInAdSegmentValuePropIds.length === 0) {
      return;
    }
    await invoker
      .delete(conversationSubjectCopyTable)
      .where(
        and(
          inArray(
            conversationSubjectCopyTable.adSegmentValuePropId,
            input.linkedInAdSegmentValuePropIds,
          ),
          eq(conversationSubjectCopyTable.status, input.status),
        ),
      );
  }
  async getOneById(
    id: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<ConversationSubjectCopy | null> {
    const invoker = tx ?? db;
    const result = await invoker
      .select()
      .from(conversationSubjectCopyTable)
      .where(
        and(
          eq(conversationSubjectCopyTable.id, id),
          eq(conversationSubjectCopyTable.status, status),
        ),
      );
    if (!result[0]) {
      return null;
    }
    return ConversationSubjectCopy({
      id: result[0].id,
      content: result[0].content,
      type: result[0].type,
      valuePropId: result[0].adSegmentValuePropId,
      leadGenForm: result[0].leadGenFormUrn,
      status: result[0].status,
    });
  }

  async updateLeadGenFormUrn(
    input: {
      valuePropId: string;
      conversationCopyType: ConversationSubjectCopy["type"];
      leadGenFormUrn: string;
      status: "DRAFT" | "ACTIVE" | "ARCHIVED";
    },
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .update(conversationSubjectCopyTable)
      .set({ leadGenFormUrn: input.leadGenFormUrn })
      .where(
        and(
          eq(
            conversationSubjectCopyTable.adSegmentValuePropId,
            input.valuePropId,
          ),
          eq(conversationSubjectCopyTable.type, input.conversationCopyType),
          eq(conversationSubjectCopyTable.status, input.status),
        ),
      );
  }

  async getAllForAdSegment(
    adSegmentId: string,
    status: "DRAFT" | "ACTIVE" | "ARCHIVED",
    tx?: Transaction,
  ): Promise<ConversationSubjectCopy[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(conversationSubjectCopyTable)
      .where(
        and(
          eq(conversationSubjectCopyTable.adSegmentValuePropId, adSegmentId),
          eq(conversationSubjectCopyTable.status, status),
        ),
      );
    return res.map((each) =>
      ConversationSubjectCopy({
        id: each.id,
        content: each.content,
        type: each.type,
        valuePropId: each.adSegmentValuePropId,
        leadGenForm: each.leadGenFormUrn,
        status: each.status,
      }),
    );
  }

  async updateManyToActive(
    input: {
      ids: string[];
    },
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (input.ids.length === 0) {
      return;
    }
    await invoker
      .update(conversationSubjectCopyTable)
      .set({ status: "ACTIVE" })
      .where(
        and(
          inArray(conversationSubjectCopyTable.id, input.ids),
          eq(conversationSubjectCopyTable.status, "DRAFT"),
        ),
      );
  }

  async getManyByIds(
    ids: string[],
    tx?: Transaction,
  ): Promise<ConversationSubjectCopy[]> {
    const invoker = tx ?? db;
    if (ids.length === 0) {
      return [];
    }
    const res = await invoker
      .select()
      .from(conversationSubjectCopyTable)
      .where(inArray(conversationSubjectCopyTable.id, ids));
    return res.map((each) =>
      ConversationSubjectCopy({
        id: each.id,
        content: each.content,
        type: each.type,
        valuePropId: each.adSegmentValuePropId,
        leadGenForm: each.leadGenFormUrn,
        status: each.status,
      }),
    );
  }
}
