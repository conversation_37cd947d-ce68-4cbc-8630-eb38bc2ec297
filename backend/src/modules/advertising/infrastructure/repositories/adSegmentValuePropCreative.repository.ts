import { and, eq, inArray } from "drizzle-orm";

import { db } from "../../../../database/db";
import { Transaction } from "../../../../database/dbTransactionType";
import { adCreativeTable } from "../../../../database/schemas/advertising/adCreative.table";
import { adSegmentValuePropCreativeTable } from "../../../../database/schemas/advertising/adSegmentValuePropCreative.table";
import { linkedInAdProgramCreativeTable } from "../../../../database/schemas/advertising/linkedInAdProgramCreative.table";
import { linkedInAdSegmentValuePropTable } from "../../../../database/schemas/advertising/linkedInAdSegmentValueProp.table";
import { IAdSegmentValuePropCreativeRepository } from "../../application/interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";
import { AdCreative } from "../../domain/entites/adCreative";
import { LinkedInAdProgramCreative } from "../../domain/entites/linkedInAdProgramCreative";
import { ValuePropCreative } from "../../domain/entites/valuePropCreative";

export class AdSegmentValuePropCreativeRepository
  implements IAdSegmentValuePropCreativeRepository
{
  async createOne(
    valuePropCreative: ValuePropCreative,
    tx?: Transaction,
  ): Promise<ValuePropCreative> {
    const invoker = tx ?? db;
    const res = await invoker
      .insert(adSegmentValuePropCreativeTable)
      .values(valuePropCreative);
    return valuePropCreative;
  }

  async createMany(
    valuePropCreatives: ValuePropCreative[],
    tx?: Transaction,
  ): Promise<ValuePropCreative[]> {
    const invoker = tx ?? db;
    if (valuePropCreatives.length === 0) {
      return [];
    }
    const res = await invoker
      .insert(adSegmentValuePropCreativeTable)
      .values(valuePropCreatives)
      .onConflictDoNothing({
        target: [
          adSegmentValuePropCreativeTable.adSegmentValuePropId,
          adSegmentValuePropCreativeTable.adProgramCreativeId,
        ],
      });
    return valuePropCreatives;
  }

  async getOne(
    id: string,
    tx?: Transaction,
  ): Promise<ValuePropCreative | null> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(adSegmentValuePropCreativeTable)
      .where(eq(adSegmentValuePropCreativeTable.id, id));
    return res[0] ?? null;
  }
  async getAllForValueProp(
    valuePropId: string,
    tx?: Transaction,
  ): Promise<ValuePropCreative[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(adSegmentValuePropCreativeTable)
      .where(
        eq(adSegmentValuePropCreativeTable.adSegmentValuePropId, valuePropId),
      );
    return res;
  }
  async deleteOne(id: string, tx?: Transaction): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(adSegmentValuePropCreativeTable)
      .where(eq(adSegmentValuePropCreativeTable.id, id));
  }

  async deleteAllForValueProp(
    valuePropId: string,
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    await invoker
      .delete(adSegmentValuePropCreativeTable)
      .where(
        eq(adSegmentValuePropCreativeTable.adSegmentValuePropId, valuePropId),
      );
  }

  async deleteAllForManyValueProps(
    valuePropIds: string[],
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (valuePropIds.length === 0) {
      return;
    }
    await invoker
      .delete(adSegmentValuePropCreativeTable)
      .where(
        inArray(
          adSegmentValuePropCreativeTable.adSegmentValuePropId,
          valuePropIds,
        ),
      );
  }

  async deleteAllForAdProgramCreativeIds(
    adProgramCreativeIds: string[],
    tx?: Transaction,
  ): Promise<void> {
    const invoker = tx ?? db;
    if (adProgramCreativeIds.length === 0) {
      return;
    }
    await invoker
      .delete(adSegmentValuePropCreativeTable)
      .where(
        inArray(
          adSegmentValuePropCreativeTable.adProgramCreativeId,
          adProgramCreativeIds,
        ),
      );
  }
  async getAllForAdProgramCreativeId(
    adProgramCreativeId: string,
    tx?: Transaction,
  ): Promise<ValuePropCreative[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(adSegmentValuePropCreativeTable)
      .where(
        eq(
          adSegmentValuePropCreativeTable.adProgramCreativeId,
          adProgramCreativeId,
        ),
      );
    return res;
  }

  async getAllForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<ValuePropCreative[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(adSegmentValuePropCreativeTable)
      .innerJoin(
        linkedInAdSegmentValuePropTable,
        eq(
          adSegmentValuePropCreativeTable.adSegmentValuePropId,
          linkedInAdSegmentValuePropTable.id,
        ),
      )
      .where(
        eq(linkedInAdSegmentValuePropTable.linkedInAdSegmentId, adSegmentId),
      );
    return res.map((each) => each.ad_segment_value_prop_creative);
  }

  async getAllForAdSegmentAndAdProgramCreativeId(
    adSegmentId: string,
    adProgramCreativeId: string,
    tx?: Transaction,
  ): Promise<ValuePropCreative[]> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(adSegmentValuePropCreativeTable)
      .innerJoin(
        linkedInAdSegmentValuePropTable,
        eq(
          adSegmentValuePropCreativeTable.adSegmentValuePropId,
          linkedInAdSegmentValuePropTable.id,
        ),
      )
      .where(
        and(
          eq(
            adSegmentValuePropCreativeTable.adProgramCreativeId,
            adProgramCreativeId,
          ),
          eq(linkedInAdSegmentValuePropTable.linkedInAdSegmentId, adSegmentId),
        ),
      );
    return res.map((each) => each.ad_segment_value_prop_creative);
  }

  async getOneByAdProgramCreativeIdAndValuePropId(
    adProgramCreativeId: string,
    valuePropId: string,
    tx?: Transaction,
  ): Promise<ValuePropCreative | null> {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(adSegmentValuePropCreativeTable)
      .where(
        and(
          eq(
            adSegmentValuePropCreativeTable.adProgramCreativeId,
            adProgramCreativeId,
          ),
          eq(adSegmentValuePropCreativeTable.adSegmentValuePropId, valuePropId),
        ),
      );
    if (!res[0]) {
      return null;
    }
    return res[0];
  }

  async getManyByAdProgramCreativeIds(
    ids: string[],
    tx?: Transaction,
  ): Promise<
    {
      adSegmentValuePropCreative: ValuePropCreative;
      adProgramCreative: LinkedInAdProgramCreative;
      name: string;
    }[]
  > {
    const invoker = tx ?? db;
    if (ids.length === 0) {
      return [];
    }
    const res = await invoker
      .select({
        adSegmentValuePropCreative: adSegmentValuePropCreativeTable,
        adProgramCreative: linkedInAdProgramCreativeTable,
        creative: adCreativeTable,
      })
      .from(adSegmentValuePropCreativeTable)
      .innerJoin(
        linkedInAdProgramCreativeTable,
        eq(
          adSegmentValuePropCreativeTable.adProgramCreativeId,
          linkedInAdProgramCreativeTable.id,
        ),
      )
      .innerJoin(
        adCreativeTable,
        eq(linkedInAdProgramCreativeTable.adCreativeId, adCreativeTable.id),
      )
      .where(inArray(adSegmentValuePropCreativeTable.adProgramCreativeId, ids));
    return res.map((each) => {
      return {
        adSegmentValuePropCreative: each.adSegmentValuePropCreative,
        adProgramCreative: each.adProgramCreative,
        name: each.creative.fileName,
      };
    });
  }
}
