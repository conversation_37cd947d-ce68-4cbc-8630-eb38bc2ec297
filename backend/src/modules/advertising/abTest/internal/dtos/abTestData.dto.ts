import { z } from "zod";

import { abTestTypeSchema } from "../domain/abTestType.valueObject";

const sponsoredContentVarientsMap = z.object({
  adFormatType: z.literal("SPONSORED_CONTENT"),
  audienceType: z.string(),
  valuePropType: z.string(),
  creativeType: z.string(),
  socialPostBodyCopyType: z.string(),
  socialPostCallToActionType: z.string(),
});

const sponsoredInmailVarientsMap = z.object({
  adFormatType: z.literal("SPONSORED_INMAIL"),
  audienceType: z.string(),
  valuePropType: z.string(),
  conversationSubjectCopyType: z.string(),
  conversationMessageCopyType: z.string(),
  conversationCallToActionType: z.string(),
});

const varientsMap = z.discriminatedUnion("adFormatType", [
  sponsoredContentVarientsMap,
  sponsoredInmailVarientsMap,
]);

const currentRunningAbTestDataDtoSchema = z.object({
  abTestId: z.string().uuid(),
  status: z.enum([
    "IN_PROGRESS",
    "NOT_STARTED",
    "USER_RESOLVED",
    "AUTO_RESOLVED",
    "COMPLETED",
    "FAILED",
    "CANCELLED",
  ]),
  type: abTestTypeSchema,
  autoResolvedData: z
    .object({
      varientId: z.string().uuid(),
      varientsUsedInSponsoredCreative: varientsMap,
      adId: z.string(),
      adName: z.string(),
      metrics: z.object({
        impressions: z.number().nullable(),
        clicks: z.number().nullable(),
        conversions: z.number().nullable(),
        leads: z.number().nullable(),
        videoViews: z.number().nullable(),
        sends: z.number().nullable(),
        opens: z.number().nullable(),
        cost: z.number().nullable(),
        actionClicks: z.number().nullable(),
        totalEngagements: z.number().nullable(),
        oneClickLeadFormOpens: z.number().nullable(),
        landingPageClicks: z.number().nullable(),
        videoCompletions: z.number().nullable(),
        videoFirstQuartileCompletions: z.number().nullable(),
        videoMidpointCompletions: z.number().nullable(),
        videoThirdQuartileCompletions: z.number().nullable(),
        videoStarts: z.number().nullable(),
        externalWebsiteConversions: z.number().nullable(),
      }),
    })
    .nullable(),
  currentRound: z
    .object({
      id: z.string().uuid(),
      roundIndex: z.number(),
      currentBestId: z.string().uuid(),
      contenderId: z.string().uuid(),
    })
    .nullable(),
  pastRounds: z.array(
    z.object({
      id: z.string().uuid(),
      roundIndex: z.number(),
      currentBestId: z.string().uuid(),
      contenderId: z.string().uuid(),
      winner: z.enum(["CURRENT_BEST", "CONTENDER"]),
    }),
  ),
  upcomingRounds: z.array(
    z.object({
      id: z.string().uuid(),
      roundIndex: z.number(),
      currentBestId: z.string().uuid(),
      contenderId: z.string().uuid(),
    }),
  ),
});

const abTestDataDtoSchema = z.discriminatedUnion("status", [
  currentRunningAbTestDataDtoSchema,
]);

export type AbTestDataDto = z.infer<typeof abTestDataDtoSchema>;
