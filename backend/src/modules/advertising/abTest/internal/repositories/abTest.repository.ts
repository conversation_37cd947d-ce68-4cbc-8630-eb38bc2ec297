import { asc, eq } from "drizzle-orm";
import { err, ok, Result } from "neverthrow";

import { db, Transaction } from "../../../../../database/db";
import {
  audienceTestTable,
  conversationCallToActionTestTable,
  conversationMessageCopyTestTable,
  conversationSubjectTestTable,
  creativeTestTable,
  socialPostBodyCopyTestTable,
  socialPostCallToActionTestTable,
  valuePropTestTable,
} from "../../../../../database/schemas/advertising/abTests/abTestTables";
import { linkedInAdAccountTable } from "../../../../../database/schemas/advertising/linkedInAdAccount.table";
import { stageTable } from "../../../../../database/schemas/advertising/stage.table";
import { AbTest } from "../domain/abTest.entity";
import { AbTestType } from "../domain/abTestType.valueObject";
import { IAbTestRepository } from "./abTest.repository.interface";
import { abTestTableRegistry } from "./abTestTables.registry";

export class AbTestRepository implements IAbTestRepository {
  async createOne(
    input: AbTest,
    tx: Transaction,
  ): Promise<
    Result<
      AbTest,
      { type: "STAGE_NOT_FOUND" | "AB_TEST_ALREADY_EXISTS_FOR_STAGE" }
    >
  > {
    const invoker = tx ?? db;
    const abTestTable = abTestTableRegistry[input.type];
    try {
      await invoker.insert(abTestTable.abTestTable).values(input);
      return ok(input);
    } catch (error) {
      const errObject = error as {
        code: string;
      };
      if (errObject.code === "23505") {
        return err({
          type: "AB_TEST_ALREADY_EXISTS_FOR_STAGE",
        });
      }
      if (errObject.code === "23503") {
        return err({
          type: "STAGE_NOT_FOUND",
        });
      }
      throw error;
    }
  }

  async updateOne(input: AbTest, tx: Transaction): Promise<void> {
    const invoker = tx ?? db;
    const abTestTable = abTestTableRegistry[input.type].abTestTable;
    await invoker
      .update(abTestTable)
      .set(input)
      .where(eq(abTestTable.stageId, input.stageId));
  }

  async getOne(
    stageId: string,
    type: AbTestType,
    tx?: Transaction,
  ): Promise<AbTest | null> {
    const invoker = tx ?? db;
    const abTestTable = abTestTableRegistry[type].abTestTable;
    const res = await invoker
      .select()
      .from(abTestTable)
      .where(eq(abTestTable.stageId, stageId));
    if (!res[0]) {
      return null;
    }

    if (
      res[0].status !== "COMPLETED" &&
      res[0].status !== "AUTO_RESOLVED" &&
      res[0].status !== "USER_RESOLVED"
    ) {
      return AbTest({
        stageId: res[0].stageId,
        type: type,
        status: res[0].status,
        winnerId: null,
      });
    } else {
      if (!res[0].winnerId) {
        throw new Error("WinnerId is null");
      }
      return AbTest({
        stageId: res[0].stageId,
        type: type,
        status: res[0].status,
        winnerId: res[0].winnerId,
      });
    }
  }

  async getAbTestsForAdSegment(
    adSegmentId: string,
    tx?: Transaction,
  ): Promise<
    {
      stageId: string;
      status: "PAST" | "CURRENT" | "UPCOMING";
      type: AbTestType;
      stageIndex: number;
    }[]
  > {
    const invoker = tx ?? db;
    const res = await invoker
      .select()
      .from(stageTable)
      .where(eq(stageTable.linkedInAdSegmentid, adSegmentId))
      .orderBy(asc(stageTable.index));

    const arr: {
      stageId: string;
      abTestType: AbTestType;
      status: "PAST" | "CURRENT" | "UPCOMING";
      stageIndex: number;
    }[] = [];

    for (const stage of res) {
      const status =
        stage.status == "NOT_STATED"
          ? "UPCOMING"
          : stage.status == "RUNNING"
            ? "CURRENT"
            : "PAST";
      if (stage.stageType === "audienceTest") {
        arr.push({
          stageId: stage.id,
          abTestType: "audience",
          status: status,
          stageIndex: stage.index,
        });
      } else if (stage.stageType === "valuePropTest") {
        arr.push({
          stageId: stage.id,
          abTestType: "valueProp",
          status: status,
          stageIndex: stage.index,
        });
      } else if (stage.stageType === "creativeTest") {
        arr.push({
          stageId: stage.id,
          abTestType: "creative",
          status: status,
          stageIndex: stage.index,
        });
      } else if (stage.stageType === "conversationSubjectTest") {
        arr.push({
          stageId: stage.id,
          abTestType: "conversationSubject",
          status: status,
          stageIndex: stage.index,
        });
      } else if (stage.stageType === "conversationMessageCopyTest") {
        arr.push({
          stageId: stage.id,
          abTestType: "conversationMessageCopy",
          status: status,
          stageIndex: stage.index,
        });
      } else if (stage.stageType === "conversationCallToActionTest") {
        arr.push({
          stageId: stage.id,
          abTestType: "conversationCallToAction",
          status: status,
          stageIndex: stage.index,
        });
      } else if (stage.stageType === "socialPostBodyCopyTest") {
        arr.push({
          stageId: stage.id,
          abTestType: "socialPostBodyCopy",
          status: status,
          stageIndex: stage.index,
        });
      } else if (stage.stageType === "socialPostCallToActionTest") {
        arr.push({
          stageId: stage.id,
          abTestType: "socialPostCallToAction",
          status: status,
          stageIndex: stage.index,
        });
      }
    }
    return arr.map((abTest) => ({
      stageId: abTest.stageId,
      type: abTest.abTestType,
      status: abTest.status,
      stageIndex: abTest.stageIndex,
    }));
  }
}
