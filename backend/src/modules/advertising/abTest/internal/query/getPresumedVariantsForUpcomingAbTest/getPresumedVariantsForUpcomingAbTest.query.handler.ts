import { IAbTestRepository } from "../../repositories/abTest.repository.interface";
import { DataProviderService } from "../../services/abTestDataProviders/dataProvider.service";
import { getPresumedVariantsForUpcomingAbTestQuery } from "./getPresumedVariantsForUpcomingAbTest.query.interface";

export class GetPresumedVariantsForUpcomingAbTestQueryHandler {
  constructor(
    private readonly abTestRepository: IAbTestRepository,
    private readonly dataProvider: DataProviderService,
  ) {}

  async execute(query: getPresumedVariantsForUpcomingAbTestQuery) {
    const abTest = await this.abTestRepository.getOne(
      query.stage.id,
      query.type,
      query.tx,
    );
    if (!abTest) {
      return null;
    }

    if (abTest.status !== "NOT_STARTED") {
      return null;
    }

    const variants = await this.dataProvider.getVariantsToSetupRounds({
      adSegmentId: query.stage.adSegmentId,
      abTestType: query.type,
    });

    if (variants.isErr()) {
      return null;
    }

    const names = await this.dataProvider.getNamesFromVariantIds({
      variantIds: variants.value,
    });
    return names;
  }
}
