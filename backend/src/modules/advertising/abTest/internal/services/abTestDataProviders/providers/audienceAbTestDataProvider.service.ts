import { err, ok, Result } from "neverthrow";

import { IAdSegmentValuePropRepository } from "../../../../../application/interfaces/infrastructure/repositories/adSegmentValueProp.repository.interface";
import { IAdSegmentValuePropCreativeRepository } from "../../../../../application/interfaces/infrastructure/repositories/adSegmentValuePropCreative.repository.interface";
import { IConversationSubjectCopyRepository } from "../../../../../application/interfaces/infrastructure/repositories/conversationSubjectCopy.repository.interface";
import { ILinkedInAdAudienceRepository } from "../../../../../application/interfaces/infrastructure/repositories/linkedInAdAudience.repository.interface";
import { ILinkedInCampaignRepositoryInterface } from "../../../../../application/interfaces/infrastructure/repositories/linkedInCampaign.repository.interface";
import { AdProgram } from "../../../../../domain/entites/adProgram";
import { AdSegment } from "../../../../../domain/entites/adSegment";
import { LinkedInStateInput } from "../../../../../linkedInStateOrchestrator/domain/linkedInStateInput.valueObject";
import { LinkedInStateOutput } from "../../../../../linkedInStateOrchestrator/domain/linkedInStateOutput.entity";
import { AudienceAbTestDomain } from "../../../domain/abTestTypeDataProcessors/audienceAbTestDataProcessor.domain";
import { IAbTestTypeDataProviderService } from "../abTestTypeDataProvider.serivce.interface";

export const AudienceAbTestGetVariantsToSetupRounds = (deps: {
  campaignRepository: ILinkedInCampaignRepositoryInterface;
  adSegmentValuePropCreativeRepository: IAdSegmentValuePropCreativeRepository;
  conversationSubjectCopyRepository: IConversationSubjectCopyRepository;
  adAudienceRepository: ILinkedInAdAudienceRepository;
}): IAbTestTypeDataProviderService => ({
  async getVariantsToSetupRounds(input: {
    adProgram: AdProgram;
    adSegment: AdSegment;
  }) {
    const shared = async (): Promise<
      Result<string[], { type: "COULD_NOT_GET_DATA_TO_SETUP_ROUNDS" }>
    > => {
      const campaigns = await deps.campaignRepository.getManyForAdSegment(
        input.adSegment.id,
      );
      return ok(
        AudienceAbTestDomain.constructResourceIdsList({
          campaigns,
        }),
      );
    };

    return ok({
      SPONSORED_CONTENT: shared,
      SPONSORED_CONVERSATION: shared,
      SPONSORED_INMAIL: shared,
    });
  },

  async getNamesFromVariantIds(input: { variantIds: string[] }) {
    const arr = [];
    for (const variantId of input.variantIds) {
      const audience = await deps.adAudienceRepository.getOne(variantId);
      if (audience) {
        arr.push({
          variantId,
          name: audience.audienceTargetCriteria.include.and
            .map((each) => each.or.map((each) => each.facetName))
            .join(", "),
        });
      }
    }
    return arr;
  },

  async getLinkedInStateOrchestratorInput(input: {
    contenderVariantId: string;
    currentBestVariantId: string;
    adProgram: AdProgram;
    adSegment: AdSegment;
  }) {
    const SPONSORED_CONTENT = async (): Promise<
      Result<
        LinkedInStateInput,
        {
          type:
            | "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT"
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
        }
      >
    > => {
      const valuePropCreatives =
        await deps.adSegmentValuePropCreativeRepository.getAllForAdSegment(
          input.adSegment.id,
        );

      const valuePropCreative = valuePropCreatives[0];
      if (!valuePropCreative) {
        return err({ type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT" });
      }

      return ok(
        AudienceAbTestDomain.constructLinkerInStateOrchestratorInput.forSponsoredContent(
          {
            adSegmentId: input.adSegment.id,
            currentBestAudienceId: input.currentBestVariantId,
            contenderAudienceId: input.contenderVariantId,
            creativeId: valuePropCreative.adProgramCreativeId,
            valuePropId: valuePropCreative.adSegmentValuePropId,
            bestSocialPostBodyCopyType: null,
            bestSocialPostCallToActionType: null,
            adProgram: input.adProgram,
          },
        ),
      );
    };

    const SPONSORED_INMAIL = async (): Promise<
      Result<
        LinkedInStateInput,
        {
          type:
            | "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT"
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED";
        }
      >
    > => {
      const conversationSubjectCopy = (
        await deps.conversationSubjectCopyRepository.getAllForAdSegment(
          input.adSegment.id,
          "ACTIVE",
        )
      )[0];
      if (!conversationSubjectCopy) {
        return err({ type: "COULD_NOT_GET_LINKEDIN_STATE_ORCHESTRATOR_INPUT" });
      }

      const stateInput =
        AudienceAbTestDomain.constructLinkerInStateOrchestratorInput.forSponsoredInmail(
          {
            adSegmentId: input.adSegment.id,
            currentBestAudienceId: input.currentBestVariantId,
            contenderAudienceId: input.contenderVariantId,
            valuePropId: conversationSubjectCopy.valuePropId,
            conversationSubjectType: conversationSubjectCopy.content,
            conversationMessageType: null,
            conversationCallToActionType: null,
          },
        );
      return ok(stateInput);
    };

    const SPONSORED_CONVERSATION = async (): Promise<
      Result<
        LinkedInStateInput,
        { type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" }
      >
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };
    return ok({
      SPONSORED_CONTENT,
      SPONSORED_INMAIL,
      SPONSORED_CONVERSATION,
    });
  },

  async getMetricsForDecisionEngine(input: {
    adProgram: AdProgram;
    adSegment: AdSegment;
    contenderVariantId: string;
    currentBestVariantId: string;
    linkedInStateOutput: LinkedInStateOutput;
  }) {
    const SPONSORED_CONTENT = async (): Promise<
      Result<
        {
          currentBestMetrics: {
            primaryMetric: number;
            secondaryMetric: number;
          };
          contenderMetrics: { primaryMetric: number; secondaryMetric: number };
        },
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE";
        }
      >
    > => {
      const res =
        await AudienceAbTestDomain.constructMetricsForDecisionEngine.forSponsoredContent(
          {
            adProgram: input.adProgram,
            contenderAudienceId: input.contenderVariantId,
            currentBestAudienceId: input.currentBestVariantId,
            linkedInStateOutput: input.linkedInStateOutput,
          },
        );
      if (res.isErr()) {
        return err({ type: "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE" });
      }
      return ok(res.value);
    };

    const SPONSORED_INMAIL = async (): Promise<
      Result<
        {
          currentBestMetrics: {
            primaryMetric: number;
            secondaryMetric: number;
          };
          contenderMetrics: { primaryMetric: number; secondaryMetric: number };
        },
        {
          type:
            | "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED"
            | "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE";
        }
      >
    > => {
      const res =
        await AudienceAbTestDomain.constructMetricsForDecisionEngine.forSponsoredInmail(
          {
            adProgram: input.adProgram,
            contenderAudienceId: input.contenderVariantId,
            currentBestAudienceId: input.currentBestVariantId,
            linkedInStateOutput: input.linkedInStateOutput,
          },
        );
      if (res.isErr()) {
        return err({ type: "COULD_NOT_GET_METRICS_FOR_DECISION_ENGINE" });
      }
      return ok(res.value);
    };

    const SPONSORED_CONVERSATION = async (): Promise<
      Result<
        {
          currentBestMetrics: {
            primaryMetric: number;
            secondaryMetric: number;
          };
          contenderMetrics: { primaryMetric: number; secondaryMetric: number };
        },
        { type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" }
      >
    > => {
      return err({ type: "AD_PROGRAM_FORMAT_TYPE_NOT_SUPPORTED" });
    };

    return ok({
      SPONSORED_CONTENT,
      SPONSORED_INMAIL,
      SPONSORED_CONVERSATION,
    });
  },
});
