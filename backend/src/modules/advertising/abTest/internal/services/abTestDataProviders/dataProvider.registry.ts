import { AdSegmentBestVariantRepository } from "../../../../infrastructure/repositories/adSegmentBestVariant.repository";
import { AdSegmentValuePropRepository } from "../../../../infrastructure/repositories/adSegmentValueProp.repository";
import { AdSegmentValuePropCreativeRepository } from "../../../../infrastructure/repositories/adSegmentValuePropCreative.repository";
import { ConversationSubjectCopyRepository } from "../../../../infrastructure/repositories/conversationSubjectCopy.repository";
import { LinkedInAdAudienceRepository } from "../../../../infrastructure/repositories/linkedInAdAudience.repository";
import { LinkedInAdProgramRepository } from "../../../../infrastructure/repositories/linkedInAdProgram.repository";
import { LinkedInAdSegmentRepository } from "../../../../infrastructure/repositories/linkedInAdSegment.repository";
import { LinkedInCampaignRepository } from "../../../../infrastructure/repositories/linkedInCampaign.repository";
import { AbTestType } from "../../domain/abTestType.valueObject";
import { IAbTestTypeDataProviderService } from "./abTestTypeDataProvider.serivce.interface";
import { AudienceAbTestGetVariantsToSetupRounds } from "./providers/audienceAbTestDataProvider.service";
import { ConversationSubjectAbTestGetVariantsToSetupRounds } from "./providers/conversationSubjectDataProvider.service";
import { CreativeAbTestGetVariantsToSetupRounds } from "./providers/creativeAbTestDataProvider.service";
import { ValuePropAbTestGetVariantsToSetupRounds } from "./providers/valuePropAbTestDataProvider.service";

type DataProviderRegistry = {
  [K in AbTestType]: IAbTestTypeDataProviderService;
};

export const DataProviderRegistry: DataProviderRegistry = {
  audience: AudienceAbTestGetVariantsToSetupRounds({
    campaignRepository: new LinkedInCampaignRepository(),
    adSegmentValuePropCreativeRepository:
      new AdSegmentValuePropCreativeRepository(),
    conversationSubjectCopyRepository: new ConversationSubjectCopyRepository(),
    adAudienceRepository: new LinkedInAdAudienceRepository(),
  }),
  valueProp: ValuePropAbTestGetVariantsToSetupRounds({
    adSegmentValuePropRepository: new AdSegmentValuePropRepository(),
    adSegmentValuePropCreativeRepository:
      new AdSegmentValuePropCreativeRepository(),
    adSegmentBestVariantRepository: new AdSegmentBestVariantRepository(),
    adSegmentRepository: new LinkedInAdSegmentRepository(),
    adProgramRepository: new LinkedInAdProgramRepository(),
    conversationSubjectCopyRepository: new ConversationSubjectCopyRepository(),
    linkedInCampaignRepository: new LinkedInCampaignRepository(),
  }),
  creative: CreativeAbTestGetVariantsToSetupRounds({
    adSegmentValuePropRepository: new AdSegmentValuePropRepository(),
    adSegmentValuePropCreativeRepository:
      new AdSegmentValuePropCreativeRepository(),
    adSegmentBestVariantRepository: new AdSegmentBestVariantRepository(),
    adSegmentRepository: new LinkedInAdSegmentRepository(),
    adProgramRepository: new LinkedInAdProgramRepository(),
    linkedInCampaignRepository: new LinkedInCampaignRepository(),
  }),
  conversationSubject: ConversationSubjectAbTestGetVariantsToSetupRounds({
    adSegmentValuePropRepository: new AdSegmentValuePropRepository(),
    adSegmentValuePropCreativeRepository:
      new AdSegmentValuePropCreativeRepository(),
    adSegmentBestVariantRepository: new AdSegmentBestVariantRepository(),
    adSegmentRepository: new LinkedInAdSegmentRepository(),
    adProgramRepository: new LinkedInAdProgramRepository(),
    linkedInCampaignRepository: new LinkedInCampaignRepository(),
    conversationSubjectCopyRepository: new ConversationSubjectCopyRepository(),
  }),
  conversationMessageCopy: ConversationSubjectAbTestGetVariantsToSetupRounds({
    adSegmentValuePropRepository: new AdSegmentValuePropRepository(),
    adSegmentValuePropCreativeRepository:
      new AdSegmentValuePropCreativeRepository(),
    adSegmentBestVariantRepository: new AdSegmentBestVariantRepository(),
    adSegmentRepository: new LinkedInAdSegmentRepository(),
    adProgramRepository: new LinkedInAdProgramRepository(),
    linkedInCampaignRepository: new LinkedInCampaignRepository(),
    conversationSubjectCopyRepository: new ConversationSubjectCopyRepository(),
  }),
  socialPostBodyCopy: ConversationSubjectAbTestGetVariantsToSetupRounds({
    adSegmentValuePropRepository: new AdSegmentValuePropRepository(),
    adSegmentValuePropCreativeRepository:
      new AdSegmentValuePropCreativeRepository(),
    adSegmentBestVariantRepository: new AdSegmentBestVariantRepository(),
    adSegmentRepository: new LinkedInAdSegmentRepository(),
    adProgramRepository: new LinkedInAdProgramRepository(),
    linkedInCampaignRepository: new LinkedInCampaignRepository(),
    conversationSubjectCopyRepository: new ConversationSubjectCopyRepository(),
  }),
  socialPostCallToAction: ConversationSubjectAbTestGetVariantsToSetupRounds({
    adSegmentValuePropRepository: new AdSegmentValuePropRepository(),
    adSegmentValuePropCreativeRepository:
      new AdSegmentValuePropCreativeRepository(),
    adSegmentBestVariantRepository: new AdSegmentBestVariantRepository(),
    adSegmentRepository: new LinkedInAdSegmentRepository(),
    adProgramRepository: new LinkedInAdProgramRepository(),
    linkedInCampaignRepository: new LinkedInCampaignRepository(),
    conversationSubjectCopyRepository: new ConversationSubjectCopyRepository(),
  }),
  conversationCallToAction: ConversationSubjectAbTestGetVariantsToSetupRounds({
    adSegmentValuePropRepository: new AdSegmentValuePropRepository(),
    adSegmentValuePropCreativeRepository:
      new AdSegmentValuePropCreativeRepository(),
    adSegmentBestVariantRepository: new AdSegmentBestVariantRepository(),
    adSegmentRepository: new LinkedInAdSegmentRepository(),
    adProgramRepository: new LinkedInAdProgramRepository(),
    linkedInCampaignRepository: new LinkedInCampaignRepository(),
    conversationSubjectCopyRepository: new ConversationSubjectCopyRepository(),
  }),
};
